aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
altair==5.5.0
annotated-types==0.7.0
anthropic==0.59.0
anyio==4.9.0
asyncer==0.0.8
asyncio-mqtt==0.16.2
attrs==25.3.0
backoff==2.2.1
beautifulsoup4==4.13.4
bidict==0.23.1
blinker==1.9.0
boto3==1.38.17
botocore==1.38.17
bs4==0.0.2
cachetools==5.5.2
certifi==2025.8.3
cfgv==3.4.0
chainlit==2.6.2
charset-normalizer==3.4.2
chevron==0.14.0
click==8.2.0
colorama==0.4.6
dataclasses-json==0.6.7
defusedxml==0.7.1
Deprecated==1.2.18
deprecation==2.1.0
distlib==0.4.0
distro==1.9.0
dotenv==0.9.9
faiss-cpu==1.11.0
fastapi==0.115.12
ffmpy==0.5.0
filelock==3.18.0
filetype==1.2.0
frozenlist==1.6.0
fsspec==2025.3.2
gitdb==4.0.12
GitPython==3.1.44
googleapis-common-protos==1.70.0
gotrue==2.12.3
gradio==5.29.0
gradio_client==1.10.0
greenlet==3.2.2
groovy==0.1.2
grpcio==1.74.0
h11==0.16.0
h2==4.2.0
hf-xet==1.1.1
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.31.1
hyperframe==6.1.0
identify==2.6.12
idna==3.10
importlib_metadata==8.7.0
inflection==0.5.1
iniconfig==2.1.0
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
langchain==0.3.25
langchain-community==0.3.24
langchain-core==0.3.60
langchain-text-splitters==0.3.8
langsmith==0.3.42
Lazify==0.4.0
literalai==0.1.201
lxml==6.0.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
mcp==1.12.2
mdurl==0.1.2
monotonic==1.6
mpmath==1.3.0
multidict==6.4.3
mypy_extensions==1.1.0
narwhals==1.39.1
nest-asyncio==1.6.0
networkx==3.4.2
new-package==0.0.1
nodeenv==1.9.1
numpy==2.2.5
openai==1.78.1
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
opentelemetry-exporter-otlp-proto-grpc==1.34.1
opentelemetry-exporter-otlp-proto-http==1.34.1
opentelemetry-instrumentation==0.55b1
opentelemetry-instrumentation-alephalpha==0.43.1
opentelemetry-instrumentation-anthropic==0.43.1
opentelemetry-instrumentation-bedrock==0.43.1
opentelemetry-instrumentation-chromadb==0.43.1
opentelemetry-instrumentation-cohere==0.43.1
opentelemetry-instrumentation-crewai==0.43.1
opentelemetry-instrumentation-google-generativeai==0.43.1
opentelemetry-instrumentation-groq==0.43.1
opentelemetry-instrumentation-haystack==0.43.1
opentelemetry-instrumentation-lancedb==0.43.1
opentelemetry-instrumentation-langchain==0.43.1
opentelemetry-instrumentation-llamaindex==0.43.1
opentelemetry-instrumentation-logging==0.55b1
opentelemetry-instrumentation-marqo==0.43.1
opentelemetry-instrumentation-mcp==0.43.1
opentelemetry-instrumentation-milvus==0.43.1
opentelemetry-instrumentation-mistralai==0.43.1
opentelemetry-instrumentation-ollama==0.43.1
opentelemetry-instrumentation-openai==0.43.1
opentelemetry-instrumentation-openai-agents==0.43.1
opentelemetry-instrumentation-pinecone==0.43.1
opentelemetry-instrumentation-qdrant==0.43.1
opentelemetry-instrumentation-redis==0.55b1
opentelemetry-instrumentation-replicate==0.43.1
opentelemetry-instrumentation-requests==0.55b1
opentelemetry-instrumentation-sagemaker==0.43.1
opentelemetry-instrumentation-sqlalchemy==0.55b1
opentelemetry-instrumentation-threading==0.55b1
opentelemetry-instrumentation-together==0.43.1
opentelemetry-instrumentation-transformers==0.43.1
opentelemetry-instrumentation-urllib3==0.55b1
opentelemetry-instrumentation-vertexai==0.43.1
opentelemetry-instrumentation-watsonx==0.43.1
opentelemetry-instrumentation-weaviate==0.43.1
opentelemetry-proto==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
opentelemetry-semantic-conventions-ai==0.4.11
opentelemetry-util-http==0.55b1
orjson==3.10.18
outcome==1.3.0.post0
packaging==24.2
paho-mqtt==2.1.0
pandas==2.2.3
pillow==11.2.1
platformdirs==4.3.8
pluggy==1.5.0
postgrest==1.1.1
posthog==3.25.0
pre_commit==4.2.0
propcache==0.3.1
protobuf==5.29.5
psycopg2-binary==2.9.10
pyarrow==20.0.0
pydantic==2.11.7
pydantic-settings==2.9.1
pydantic_core==2.33.2
pydeck==0.9.1
pydub==0.25.1
Pygments==2.19.1
PyJWT==2.10.1
pypdf==5.5.0
PySocks==1.7.1
pytest==8.3.5
pytest-asyncio==1.0.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-engineio==4.12.2
python-multipart==0.0.18
python-socketio==5.13.0
pytz==2025.2
PyYAML==6.0.2
realtime==2.6.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.25.0
ruff==0.11.9
s3transfer==0.12.0
safehttpx==0.1.6
selenium==4.34.2
semantic-version==2.10.0
shellingham==1.5.4
simple-websocket==1.1.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.7
-e git+https://github.com/thequinn/sports-scribe.git@de3f134ba60a71bd0b309b443c57e28010f4bbb6#egg=sport_scribe_ai_backend&subdirectory=ai-backend
SQLAlchemy==2.0.41
sse-starlette==2.4.1
starlette==0.46.2
storage3==0.12.0
streamlit==1.45.1
StrEnum==0.4.15
structlog==25.4.0
supabase==2.17.0
supafunc==0.10.1
sympy==1.14.0
syncer==2.0.3
tenacity==9.1.2
tiktoken==0.9.0
tokenizers==0.21.2
toml==0.10.2
tomli==2.2.1
tomlkit==0.13.2
torch==2.2.2
torchvision==0.17.2
tornado==6.5
tqdm==4.67.1
traceloop-sdk==0.43.1
trio==0.30.0
trio-websocket==0.12.2
typer==0.15.3
types-setuptools==80.9.0.20250529
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.14.1
tzdata==2025.2
urllib3==2.5.0
utils==1.0.2
utils-security==10
uvicorn==0.34.2
uvloop==0.21.0
virtualenv==20.33.1
watchdog==6.0.0
watchfiles==0.20.0
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
wsproto==1.2.0
yarl==1.20.0
youtube-transcript-api==1.0.3
zipp==3.23.0
zstandard==0.23.0
